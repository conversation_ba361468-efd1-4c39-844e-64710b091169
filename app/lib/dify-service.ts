// AI Configuration for Volcano Engine (OpenAI-compatible)
const difyConfig = {
  url: process.env.DIFY_API_ENDPOINT!,
  key: process.env.DIFY_API_KEY!,
};

interface DifyMessage {
  id: string;
  conversation_id: string;
  inputs: any;
  query: string;
  answer: string;
  created_at: number;
}

interface DifyMessagesResponse {
  limit: number;
  has_more: boolean;
  data: DifyMessage[];
}

interface DifyConversation {
  id: string;
  name: string;
  inputs: any;
  status: string;
  created_at: number;
}

interface DifyConversationsResponse {
  limit: number;
  has_more: boolean;
  data: DifyConversation[];
}

interface DifyRenameConversationResponse {
  result: "success";
}

interface AgentThought {
  event: "agent_thought";
  id: string;
  task_id: string;
  message_id: string;
  position: number;
  thought: string;
  observation: string;
  tool: string;
  tool_input: string;
  created_at: number;
  message_files: string[];
  conversation_id: string;
}

interface MessageFile {
  event: "message_file";
  id: string;
  type: string;
  belongs_to: string;
  url: string;
  conversation_id: string;
}

interface AgentMessage {
  event: "agent_message";
  id: string;
  task_id: string;
  message_id: string;
  answer: string;
  created_at: number;
  conversation_id: string;
}

interface MessageEnd {
  event: "message_end";
  id: string;
  conversation_id: string;
  metadata: {
    usage: {
      prompt_tokens: number;
      prompt_unit_price: string;
      prompt_price_unit: string;
      prompt_price: string;
      completion_tokens: number;
      completion_unit_price: string;
      completion_price_unit: string;
      completion_price: string;
      total_tokens: number;
      total_price: string;
      currency: string;
      latency: number;
    };
    retriever_resources?: Array<{
      position: number;
      dataset_id: string;
      dataset_name: string;
      document_id: string;
      document_name: string;
      segment_id: string;
      score: number;
      content: string;
    }>;
  };
}

export type DifyChatMessageStreamResponse =
  | AgentThought
  | MessageFile
  | AgentMessage
  | MessageEnd;

export async function getDifyConversationDetail(
  user: string,
  conversation_id: string,
  first_id?: string
): Promise<DifyMessagesResponse> {
  try {
    const response = await fetch(
      `${difyConfig.url}/messages?user=${user}&conversation_id=${conversation_id}&first_id=${first_id}`,
      {
        headers: {
          Authorization: `Bearer ${difyConfig.key}`,
        },
      }
    );
    return await response.json();
  } catch (e) {
    throw e;
  }
}

export async function getDifyConversationList(
  user: string,
  last_id?: string
): Promise<DifyConversationsResponse> {
  try {
    const response = await fetch(
      `${difyConfig.url}/conversations?user=${user}&last_id=${last_id}`,
      {
        headers: {
          Authorization: `Bearer ${difyConfig.key}`,
        },
      }
    );
    return await response.json();
  } catch (e) {
    throw e;
  }
}

export async function RenameDifyConversation(
  conversation_id: string,
  user: string,
  name: string
): Promise<DifyRenameConversationResponse> {
  try {
    const response = await fetch(
      `${difyConfig.url}/conversations/${conversation_id}/name`,
      {
        headers: {
          Authorization: `Bearer ${difyConfig.key}`,
        },
        method: "POST",
        body: JSON.stringify({
          user,
          name,
        }),
      }
    );
    return await response.json();
  } catch (e) {
    throw e;
  }
}

export async function SendDifyMessage(
  user: string,
  query: string,
  input: [],
  conversation_id?: string
): Promise<Response> {
  // This function returns a streaming response.
  // The caller should handle the stream of events.
  // Example of handling the stream:
  // const response = await SendDifyMessage(...);
  // const reader = response.body?.getReader();
  // const decoder = new TextDecoder();
  // while (true) {
  //   const { done, value } = await reader.read();
  //   if (done) break;
  //   const chunk = decoder.decode(value);
  //   // Each chunk can contain one or more events, separated by newlines.
  //   const events = chunk.split('\n').filter(line => line.startsWith('data: '));
  //   for (const event of events) {
  //     const data = JSON.parse(event.substring(6)) as DifyChatMessageStreamResponse;
  //     // Process each event based on its type
  //     console.log(data);
  //   }
  // }
  try {
    const response = await fetch(`${difyConfig.url}/chat-messages`, {
      headers: {
        Authorization: `Bearer ${difyConfig.key}`,
      },
      method: "POST",
      body: JSON.stringify({
        conversation_id,
        user,
        query,
        input,
        response_mode: "streaming",
      }),
    });
    return response;
  } catch (e) {
    throw e;
  }
}
