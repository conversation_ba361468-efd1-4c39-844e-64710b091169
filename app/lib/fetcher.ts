export const fetcher = {
  get: <T>(url: string): Promise<T> => fetch(url).then(res => {
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  }),
  post: <T>(url: string, body: any, options?: { responseType?: string }): Promise<any> => {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    return fetch(url, { 
      method: 'POST', 
      body: JSON.stringify(body), 
      headers 
    }).then(res => {
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      if (options?.responseType === 'stream') {
        return res;
      }
      return res.json();
    });
  },
  delete: <T>(url: string): Promise<T> => fetch(url, { method: 'DELETE' }).then(res => {
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  }),
};