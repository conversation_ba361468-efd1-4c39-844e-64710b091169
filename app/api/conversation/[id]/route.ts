import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { getDifyConversationDetail } from "@/app/lib/dify-service";

// GET /api/conversation/[id] - 获取特定对话及其消息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; first_id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, first_id } = await params;

    const conersation = await prisma.conversation.findUnique({
      where: {
        id,
        userId: session.user.id,
      },
      select: {
        difyId: true,
        resumeData: true,
      },
    });

    if (!conersation) {
      return Response.json({ error: "Chat not found" }, { status: 404 });
    }

    const { difyId, resumeData } = conersation;
    const response = await getDifyConversationDetail(
      session.user.id,
      difyId,
      first_id
    );

    return Response.json({
      messages: response,
      resumeData: resumeData,
    });
  } catch (error) {
    console.error("Failed to load chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/conversation/[id] - 删除对话
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    await prisma.conversation.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: {
        isDeleted: true,
      },
    });

    return Response.json({
      success: true,
    });
  } catch (error) {
    console.error("Failed to delete chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
