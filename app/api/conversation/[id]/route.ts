import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import {
  getDifyConversationDetail,
  SendDifyMessage,
} from "@/app/lib/dify-service";
import { checkMembershipStatus } from "@/app/lib/membership";

// GET /api/conversation/[id] - 获取特定对话及其消息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; first_id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, first_id } = await params;

    const conersation = await prisma.conversation.findUnique({
      where: {
        id,
        userId: session.user.id,
      },
      select: {
        difyId: true,
        resumeData: true,
      },
    });

    if (!conersation) {
      return Response.json({ error: "Chat not found" }, { status: 404 });
    }

    const { difyId, resumeData } = conersation;
    const response = await getDifyConversationDetail(
      session.user.id,
      difyId,
      first_id
    );

    return Response.json({
      messages: response,
      resumeData: resumeData,
    });
  } catch (error) {
    console.error("Failed to load chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/conversation/[id] - 发送消息到对话
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查会员状态
    const membershipStatus = await checkMembershipStatus(session.user.id);
    if (!membershipStatus.canChat) {
      return Response.json(
        { error: "Membership required", membershipRequired: true },
        { status: 403 }
      );
    }

    const { id } = await params;
    const { userInput, locale, currentResumeData } = await request.json();

    // 获取本地对话记录
    const conversation = await prisma.conversation.findUnique({
      where: {
        id,
        userId: session.user.id,
      },
      select: {
        difyId: true,
        resumeData: true,
      },
    });

    if (!conversation) {
      return Response.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    // 准备发送给Dify的输入数据
    const inputs = {
      current_resume_data: currentResumeData || conversation.resumeData || {},
      locale: locale || "zh",
    };

    // 发送消息到Dify
    const difyResponse = await SendDifyMessage(
      session.user.id,
      userInput,
      inputs as any,
      conversation.difyId || undefined
    );

    if (!difyResponse.ok) {
      throw new Error("Failed to send message to Dify");
    }

    // 处理Dify的流式响应
    const reader = difyResponse.body?.getReader();
    const decoder = new TextDecoder();
    let assistantMessage = "";
    let conversationId = conversation.difyId;
    let resumeData = null;

    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const events = chunk
          .split("\n")
          .filter((line) => line.startsWith("data: "));

        for (const event of events) {
          try {
            const data = JSON.parse(event.substring(6));

            if (data.event === "agent_message") {
              assistantMessage += data.answer;
            } else if (data.event === "message_end") {
              conversationId = data.conversation_id;
              // 这里可以处理简历数据更新逻辑
              if (data.metadata?.resume_data) {
                resumeData = data.metadata.resume_data;
              }
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }

    // 如果是新对话，更新difyId
    if (!conversation.difyId && conversationId) {
      await prisma.conversation.update({
        where: { id },
        data: { difyId: conversationId },
      });
    }

    // 更新简历数据（如果有）
    if (currentResumeData) {
      await prisma.conversation.update({
        where: { id },
        data: { resumeData: currentResumeData },
      });
    }

    return Response.json({
      message: assistantMessage,
      resumeData: resumeData,
    });
  } catch (error) {
    console.error("Failed to send message:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PATCH /api/conversation/[id] - 更新对话数据
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const { resumeData } = await request.json();

    await prisma.conversation.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: {
        resumeData: resumeData,
      },
    });

    return Response.json({
      success: true,
    });
  } catch (error) {
    console.error("Failed to update conversation:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/conversation/[id] - 删除对话
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    await prisma.conversation.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: {
        isDeleted: true,
      },
    });

    return Response.json({
      success: true,
    });
  } catch (error) {
    console.error("Failed to delete chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
