import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { getDifyConversationList } from "@/app/lib/dify-service";

// GET /api/conversation - 获取会话列表（代理Dify API）
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20", 20);
    const last_id = searchParams.get("last_id");
    const q = searchParams.get("q"); // 搜索查询

    // 如果有搜索查询，从本地数据库搜索
    if (q) {
      const conversations = await prisma.conversation.findMany({
        where: {
          userId: session.user.id,
          isDeleted: false,
          title: {
            contains: q,
          },
        },
        take: limit,
        select: {
          id: true,
          title: true,
          difyId: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      return Response.json({
        data: conversations,
      });
    }

    // 从Dify获取对话列表
    const difyResponse = await getDifyConversationList(
      session.user.id,
      last_id || undefined
    );

    // 将Dify的对话与本地数据库的对话进行匹配
    const localConversations = await prisma.conversation.findMany({
      where: {
        userId: session.user.id,
        isDeleted: false,
        difyId: {
          in: difyResponse.data.map((conv) => conv.id),
        },
      },
      select: {
        id: true,
        title: true,
        difyId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 合并Dify数据和本地数据
    const conversations = difyResponse.data.map((difyConv) => {
      const localConv = localConversations.find(
        (local) => local.difyId === difyConv.id
      );
      return {
        id: localConv?.id || difyConv.id,
        title: localConv?.title || difyConv.name,
        difyId: difyConv.id,
        createdAt:
          localConv?.createdAt ||
          new Date(difyConv.created_at * 1000).toISOString(),
        updatedAt:
          localConv?.updatedAt ||
          new Date(difyConv.created_at * 1000).toISOString(),
      };
    });

    return Response.json({
      data: conversations,
      has_more: difyResponse.has_more,
      limit: difyResponse.limit,
    });
  } catch (error) {
    console.error("Failed get conversations:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/conversation - 创建新对话
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { title, resumeData } = await request.json();

    // 在本地数据库创建对话记录
    const conversation = await prisma.conversation.create({
      data: {
        userId: session.user.id,
        title: title || "新对话",
        resumeData: resumeData || null,
        isDeleted: false,
        difyId: "", // 将在第一次发送消息时设置
      },
      select: {
        id: true,
        title: true,
        resumeData: true,
        difyId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      data: conversation,
    });
  } catch (error) {
    console.error("Failed to create conversation:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
