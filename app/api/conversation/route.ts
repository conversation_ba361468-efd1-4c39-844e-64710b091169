import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/conversation - 获取会话列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20", 20);
    const cursor = searchParams.get("cursor");
    const q = searchParams.get("q"); // 搜索查询

    // 如果有搜索查询，执行搜索
    if (q) {
      const conversations = await prisma.conversation.findMany({
        where: {
          userId: session.user.id,
          isDeleted: false,
          title: {
            contains: q,
            mode: "insensitive",
          },
        },
        take: limit,
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      return Response.json({
        data: conversations,
      });
    }

    // 正常的分页查询
    const conversations = await prisma.conversation.findMany({
      where: {
        userId: session.user.id,
        isDeleted: false,
      },
      take: limit,
      ...(cursor && {
        skip: 1,
        cursor: {
          id: cursor,
        },
      }),
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    let nextCursor = null;
    if (conversations.length === limit) {
      nextCursor = conversations[conversations.length - 1].id;
    }

    return Response.json({
      data: conversations,
      nextCursor,
    });
  } catch (error) {
    console.error("Failed get conversations:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/conversation - 创建新对话
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { title, resumeData } = await request.json();

    const conversation = await prisma.conversation.create({
      data: {
        userId: session.user.id,
        title: title || "新对话",
        resumeData: resumeData || null,
        isDeleted: false,
      },
      select: {
        id: true,
        title: true,
        resumeData: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      data: conversation,
    });
  } catch (error) {
    console.error("Failed to create conversation:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
