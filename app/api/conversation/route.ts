import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/conversations/ - 获取会话列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20", 20);
    const cursor = searchParams.get("cursor");

    const conversations = await prisma.conversation.findMany({
      where: {
        userId: session.user.id,
        isDeleted: false,
      },
      take: limit,
      ...(cursor && {
        skip: 1,
        cursor: {
          id: cursor,
        },
      }),
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    let nextCursor = null;
    if (conversations.length === limit) {
      nextCursor = conversations[conversations.length - 1].id;
    }

    return Response.json({
      items: conversations,
      nextCursor,
    });
  } catch (error) {
    console.error("Failed get conversations:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
